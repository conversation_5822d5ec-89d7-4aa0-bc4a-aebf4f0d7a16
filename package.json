{"name": "happi-reminder-desktop", "version": "1.0.0", "description": "桌面便签提醒应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["reminder", "desktop", "sticky-notes", "electron"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.happi.reminder", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}