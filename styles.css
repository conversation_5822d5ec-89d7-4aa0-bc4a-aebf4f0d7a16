/* Happi Reminder - 基于1.css的设计风格 */
:root {
  --color-primary: #fbbf24; /* bright yellow */
  --color-primary-700: #d97706; /* darker yellow for hovers */
  --color-black: #000000; /* pure black */
  --color-white: #ffffff; /* pure white */
  --color-gray: #f5f5f5; /* very light gray */
  --radius-lg: 20px;
  --radius-md: 12px;
  --radius-sm: 8px;
  --shadow-1: 0 4px 12px rgba(0,0,0,.1);
  --shadow-2: 0 8px 24px rgba(0,0,0,.15);
}

/* CSS Reset */
*, *::before, *::after { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
  color: var(--color-black);
  background: var(--color-white);
  overflow-x: hidden;
  user-select: none; /* 防止文本选择 */
}

/* 自定义标题栏 */
.titlebar {
  height: 32px;
  background: var(--color-primary);
  border-bottom: 2px solid var(--color-black);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  -webkit-app-region: drag; /* 允许拖拽窗口 */
}

.titlebar-drag {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 13px;
  flex: 1;
}

.titlebar-controls {
  display: flex;
  gap: 4px;
  -webkit-app-region: no-drag; /* 按钮区域不允许拖拽 */
}

.control-btn {
  width: 24px;
  height: 24px;
  border: 1px solid var(--color-black);
  border-radius: 4px;
  background: var(--color-white);
  color: var(--color-black);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

.close-btn:hover {
  background: #ff4757;
  border-color: #ff4757;
  color: var(--color-white);
}
img { max-width: 100%; display: block; }
a { color: inherit; text-decoration: none; }
button { font: inherit; cursor: pointer; }

/* 基础工具类 */
.container {
  width: 100%;
  margin: 0;
  padding: 12px;
  height: calc(100vh - 32px); /* 减去标题栏高度 */
  overflow-y: auto;
}

/* 页面切换 */
.main-page, .task-list-page {
  width: 100%;
  height: 100%;
}
.btn {
  display: inline-flex; align-items: center; justify-content: center;
  padding: 12px 20px; border-radius: 999px; border: 2px solid var(--color-black); cursor: pointer;
  background: var(--color-white); color: var(--color-black); transition: all .2s ease;
  font-weight: 600; gap: 8px;
}
.btn:hover { background: var(--color-black); color: var(--color-white); }
.btn-primary { background: var(--color-primary); color: var(--color-black); border-color: var(--color-black); }
.btn-primary:hover { background: var(--color-primary-700); }



/* 快速添加区域 */
.quick-add-section {
  background: var(--color-white);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 16px;
}

.quick-add-section h2 {
  font-size: 18px;
  font-weight: 800;
  margin-bottom: 16px;
  text-align: center;
}

.quick-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

#quickTaskInput {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
}

#quickTaskInput:focus {
  outline: none;
  background: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.3);
}

.quick-add-btn {
  width: 40px;
  height: 40px;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-add-btn:hover {
  background: var(--color-primary-700);
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  padding: 10px 12px;
  background: var(--color-gray);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.action-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

/* 定时提醒表单 */
.time-form {
  background: var(--color-gray);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-lg);
  padding: 16px;
  margin-bottom: 16px;
}

.time-form h3 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 12px;
  text-align: center;
}

.form-group {
  margin-bottom: 16px;
}

#timedTaskInput {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  background: var(--color-white);
  transition: all 0.2s ease;
}

#timedTaskInput:focus {
  outline: none;
  background: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.3);
}

/* 时间滑动选择器 */
.time-slider-container {
  background: var(--color-white);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
}

.time-sliders {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}
.slider-group {
  text-align: center;
}
.slider-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}
.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: var(--color-white);
  border: 2px solid var(--color-black);
  outline: none;
  margin-bottom: 8px;
  cursor: pointer;
}
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  cursor: pointer;
}
.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  cursor: pointer;
}
.slider-group span {
  display: inline-block;
  background: var(--color-black);
  color: var(--color-white);
  padding: 4px 12px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 14px;
}

/* 选中时间显示 */
.selected-time {
  text-align: center;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 8px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 10px 16px;
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: var(--color-white);
  color: var(--color-black);
}

.cancel-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

.confirm-btn {
  background: var(--color-primary);
  color: var(--color-black);
}

.confirm-btn:hover {
  background: var(--color-primary-700);
}

/* 固定按钮 */
.pin-controls {
  margin-top: 16px;
}

.pin-btn {
  width: 100%;
  padding: 12px;
  background: var(--color-gray);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pin-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

.pin-btn.pinned {
  background: var(--color-primary);
  color: var(--color-black);
}

.pin-btn.pinned:hover {
  background: var(--color-primary-700);
}

/* 任务列表页面 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 2px solid var(--color-black);
}

.back-btn, .clear-btn {
  width: 36px;
  height: 36px;
  background: var(--color-white);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover, .clear-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

.page-header h2 {
  font-size: 18px;
  font-weight: 800;
  margin: 0;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 任务项 */
.task-item {
  background: var(--color-white);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: var(--color-gray);
}

.task-item.completed {
  opacity: 0.6;
  text-decoration: line-through;
}

.task-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-black);
  border-radius: 4px;
  background: var(--color-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.task-checkbox.checked {
  background: var(--color-primary);
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-text {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.3;
}

.task-time {
  font-size: 12px;
  color: rgba(0,0,0,0.6);
  font-weight: 500;
}

.task-time.expired {
  color: #ff4757;
  font-weight: 600;
}

.task-actions {
  display: flex;
  gap: 4px;
}

.task-delete-btn {
  width: 28px;
  height: 28px;
  background: var(--color-white);
  border: 2px solid var(--color-black);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.task-delete-btn:hover {
  background: #ff4757;
  border-color: #ff4757;
  color: var(--color-white);
}

/* 卡片颜色 */
.reminder-card.color-1 { background: #ff6b6b; }
.reminder-card.color-2 { background: #4ecdc4; }
.reminder-card.color-3 { background: var(--color-primary); }
.reminder-card.color-4 { background: #a8e6cf; }
.reminder-card.color-5 { background: #ffd93d; }
.reminder-card.color-6 { background: #ff8cc8; }

.reminder-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-black);
  margin-bottom: 12px;
  line-height: 1.3;
}
.reminder-time {
  background: var(--color-black);
  color: var(--color-white);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.reminder-time.expired {
  background: #ff4757;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
.reminder-actions {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}
.reminder-btn {
  background: var(--color-white);
  border: 2px solid var(--color-black);
  padding: 8px 16px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 12px;
  transition: all 0.2s ease;
}
.reminder-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(0,0,0,0.6);
  background: var(--color-gray);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-lg);
  margin: 20px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--color-black);
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .time-sliders {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .reminders-grid {
    grid-template-columns: 1fr;
  }
}
