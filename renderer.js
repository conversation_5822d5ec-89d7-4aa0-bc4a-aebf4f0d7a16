const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 全局变量
let tasks = [];
let isPinned = false;

// 窗口控制函数
function minimizeWindow() {
    ipcRenderer.send('minimize-window');
}

function closeWindow() {
    ipcRenderer.send('close-window');
}

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    setupSliders();
    await loadTasks();
    updateTaskCount();
    startReminderChecker();
});

// 设置滑动条
function setupSliders() {
    const daySlider = document.getElementById('daySlider');
    const hourSlider = document.getElementById('hourSlider');
    const minuteSlider = document.getElementById('minuteSlider');
    
    const dayValue = document.getElementById('dayValue');
    const hourValue = document.getElementById('hourValue');
    const minuteValue = document.getElementById('minuteValue');
    const selectedTime = document.getElementById('selectedTime');

    const updateTime = () => {
        const days = parseInt(daySlider.value);
        const hours = parseInt(hourSlider.value);
        const minutes = parseInt(minuteSlider.value);

        dayValue.textContent = days === 0 ? '今天' : `${days}天后`;
        hourValue.textContent = hours.toString().padStart(2, '0');
        minuteValue.textContent = minutes.toString().padStart(2, '0');

        let timeStr;
        if (days === 0) {
            timeStr = `今天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        } else if (days === 1) {
            timeStr = `明天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        } else {
            timeStr = `${days}天后 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
        selectedTime.textContent = timeStr;
    };

    daySlider.addEventListener('input', updateTime);
    hourSlider.addEventListener('input', updateTime);
    minuteSlider.addEventListener('input', updateTime);

    updateTime();
}

// 快速添加任务（无时间）
async function quickAddTask() {
    const input = document.getElementById('quickTaskInput');
    const text = input.value.trim();

    if (!text) {
        alert('请输入待办事项');
        return;
    }

    const task = {
        id: Date.now().toString(),
        text: text,
        time: null, // 无时间
        completed: false,
        created: new Date().toISOString()
    };

    tasks.push(task);
    await saveTasks();
    updateTaskCount();

    // 清空输入
    input.value = '';

    // 显示成功提示
    showToast('任务已添加');
}

// 添加定时提醒
async function addTimedReminder() {
    const input = document.getElementById('timedTaskInput');
    const daySlider = document.getElementById('daySlider');
    const hourSlider = document.getElementById('hourSlider');
    const minuteSlider = document.getElementById('minuteSlider');

    const text = input.value.trim();
    if (!text) {
        alert('请输入提醒内容');
        return;
    }

    const days = parseInt(daySlider.value);
    const hours = parseInt(hourSlider.value);
    const minutes = parseInt(minuteSlider.value);

    // 计算提醒时间
    const now = new Date();
    const reminderTime = new Date(now);
    reminderTime.setDate(now.getDate() + days);
    reminderTime.setHours(hours, minutes, 0, 0);

    const task = {
        id: Date.now().toString(),
        text: text,
        time: reminderTime.toISOString(),
        completed: false,
        created: new Date().toISOString()
    };

    tasks.push(task);
    await saveTasks();
    updateTaskCount();

    // 清空输入并隐藏表单
    input.value = '';
    toggleTimeForm();

    // 重置滑动条
    daySlider.value = 0;
    hourSlider.value = 12;
    minuteSlider.value = 0;
    setupSliders();

    showToast('定时提醒已添加');
}

// 页面切换
function showMainPage() {
    document.getElementById('mainPage').style.display = 'block';
    document.getElementById('taskListPage').style.display = 'none';
}

function showTaskList() {
    document.getElementById('mainPage').style.display = 'none';
    document.getElementById('taskListPage').style.display = 'block';
    renderTaskList();
}

// 切换定时表单
function toggleTimeForm() {
    const form = document.getElementById('timeForm');
    const isVisible = form.style.display !== 'none';
    form.style.display = isVisible ? 'none' : 'block';

    if (!isVisible) {
        document.getElementById('timedTaskInput').focus();
    }
}

// 固定/取消固定
function togglePin() {
    isPinned = !isPinned;
    const btn = document.getElementById('pinBtn');

    if (isPinned) {
        btn.classList.add('pinned');
        btn.innerHTML = '<i class="fas fa-thumbtack"></i> 已固定';
        ipcRenderer.send('set-movable', false);
    } else {
        btn.classList.remove('pinned');
        btn.innerHTML = '<i class="fas fa-thumbtack"></i> 固定位置';
        ipcRenderer.send('set-movable', true);
    }
}

// 删除任务
async function deleteTask(id) {
    tasks = tasks.filter(t => t.id !== id);
    await saveTasks();
    updateTaskCount();
    renderTaskList();
}

// 完成/取消完成任务
async function toggleTaskComplete(id) {
    const task = tasks.find(t => t.id === id);
    if (task) {
        task.completed = !task.completed;
        await saveTasks();
        updateTaskCount();
        renderTaskList();
    }
}

// 清除已完成任务
async function clearCompleted() {
    const completedCount = tasks.filter(t => t.completed).length;
    if (completedCount === 0) {
        showToast('没有已完成的任务');
        return;
    }

    if (confirm(`确定要删除 ${completedCount} 个已完成的任务吗？`)) {
        tasks = tasks.filter(t => !t.completed);
        await saveTasks();
        updateTaskCount();
        renderTaskList();
        showToast('已清除完成的任务');
    }
}

// 更新任务计数
function updateTaskCount() {
    const count = tasks.filter(t => !t.completed).length;
    document.getElementById('taskCount').textContent = count;
}

// 渲染任务列表
function renderTaskList() {
    const list = document.getElementById('taskList');

    if (tasks.length === 0) {
        list.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📋</div>
                <h3>还没有任务</h3>
                <p>回到主页添加你的第一个任务吧！</p>
            </div>
        `;
        return;
    }

    // 按创建时间排序，未完成的在前
    const sortedTasks = [...tasks].sort((a, b) => {
        if (a.completed !== b.completed) {
            return a.completed ? 1 : -1;
        }
        return new Date(b.created) - new Date(a.created);
    });

    list.innerHTML = sortedTasks.map(task => {
        let timeDisplay = '';
        if (task.time) {
            const taskTime = new Date(task.time);
            const now = new Date();
            const isExpired = taskTime < now && !task.completed;
            timeDisplay = `<div class="task-time ${isExpired ? 'expired' : ''}">${formatTimeDisplay(taskTime, now)}</div>`;
        }

        return `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-id="${task.id}">
                <div class="task-checkbox ${task.completed ? 'checked' : ''}" onclick="toggleTaskComplete('${task.id}')">
                    ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                </div>
                <div class="task-content">
                    <div class="task-text">${task.text}</div>
                    ${timeDisplay}
                </div>
                <div class="task-actions">
                    <button class="task-delete-btn" onclick="deleteTask('${task.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

// 格式化时间显示
function formatTimeDisplay(taskTime, now) {
    const diff = taskTime - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (diff < 0) {
        return '已过期';
    } else if (days > 0) {
        return `${days}天后 ${taskTime.getHours().toString().padStart(2, '0')}:${taskTime.getMinutes().toString().padStart(2, '0')}`;
    } else if (hours > 0) {
        return `${hours}小时后`;
    } else if (minutes > 0) {
        return `${minutes}分钟后`;
    } else {
        return '即将到时';
    }
}

// 显示提示消息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 50px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--color-black);
        color: var(--color-white);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}

// 保存任务到本地
async function saveTasks() {
    try {
        await ipcRenderer.invoke('save-reminders', tasks);
    } catch (error) {
        console.error('保存任务失败:', error);
    }
}

// 从本地加载任务
async function loadTasks() {
    try {
        tasks = await ipcRenderer.invoke('load-reminders') || [];
    } catch (error) {
        console.error('加载任务失败:', error);
        tasks = [];
    }
}

// 检查提醒时间
function startReminderChecker() {
    setInterval(async () => {
        const now = new Date();

        for (const task of tasks) {
            if (task.time && !task.completed && !task.notified) {
                const taskTime = new Date(task.time);

                if (now >= taskTime) {
                    // 显示系统通知
                    await ipcRenderer.invoke('show-notification', '提醒时间到了！', task.text);

                    // 标记为已通知
                    task.notified = true;
                    await saveTasks();
                }
            }
        }

        // 更新显示
        updateTaskCount();
    }, 30000); // 每30秒检查一次
}

// 快捷键支持
document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        if (e.target.id === 'quickTaskInput') {
            quickAddTask();
        } else if (e.target.id === 'timedTaskInput') {
            addTimedReminder();
        }
    }

    if (e.key === 'Escape') {
        const timeForm = document.getElementById('timeForm');
        if (timeForm.style.display !== 'none') {
            toggleTimeForm();
        }
    }
});
