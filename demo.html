<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ha<PERSON><PERSON> Reminder Demo - 桌面应用预览</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">

        <!-- 添加提醒表单 -->
        <section class="add-reminder-section">
            <div class="add-reminder-form">
                <h2>添加新提醒</h2>
                <div class="form-group">
                    <input type="text" id="taskInput" placeholder="输入你的待办事项..." maxlength="100">
                </div>
                
                <!-- 时间滑动选择器 -->
                <div class="time-slider-container">
                    <h3>选择提醒时间</h3>
                    <div class="time-sliders">
                        <div class="slider-group">
                            <label>天数 (今天起)</label>
                            <input type="range" id="daySlider" min="0" max="10" value="0" class="slider">
                            <span id="dayValue">今天</span>
                        </div>
                        <div class="slider-group">
                            <label>小时</label>
                            <input type="range" id="hourSlider" min="0" max="23" value="12" class="slider">
                            <span id="hourValue">12</span>
                        </div>
                        <div class="slider-group">
                            <label>分钟</label>
                            <input type="range" id="minuteSlider" min="0" max="59" value="0" step="5" class="slider">
                            <span id="minuteValue">00</span>
                        </div>
                    </div>
                    <div class="selected-time">
                        <i class="fas fa-clock"></i>
                        <span id="selectedTime">今天 12:00</span>
                    </div>
                </div>
                
                <button id="addBtn" class="btn btn-primary" onclick="addReminderDemo()">
                    <i class="fas fa-bell"></i> 添加提醒
                </button>
            </div>
        </section>

        <!-- 提醒列表 -->
        <section class="reminders-section">
            <h2>我的提醒</h2>
            <div class="reminders-grid" id="remindersGrid">
                <!-- 演示卡片 -->
                <div class="reminder-card color-1">
                    <div class="reminder-title">完成项目报告</div>
                    <div class="reminder-time">2小时后</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
                
                <div class="reminder-card color-2">
                    <div class="reminder-title">买菜做饭</div>
                    <div class="reminder-time">明天 18:00</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
                
                <div class="reminder-card color-3">
                    <div class="reminder-title">健身锻炼</div>
                    <div class="reminder-time">3天后 07:00</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
                
                <div class="reminder-card color-4">
                    <div class="reminder-title">朋友聚会</div>
                    <div class="reminder-time">5天后 19:30</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
                
                <div class="reminder-card color-5">
                    <div class="reminder-title">医院体检</div>
                    <div class="reminder-time">7天后 09:00</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
                
                <div class="reminder-card color-6">
                    <div class="reminder-title">过期提醒示例</div>
                    <div class="reminder-time expired">已过期</div>
                    <div class="reminder-actions">
                        <button class="reminder-btn">删除</button>
                    </div>
                </div>
            </div>
        </section>

    </div>

    <script>
        // 演示用的滑动条功能
        function setupDemoSliders() {
            const daySlider = document.getElementById('daySlider');
            const hourSlider = document.getElementById('hourSlider');
            const minuteSlider = document.getElementById('minuteSlider');
            
            const dayValue = document.getElementById('dayValue');
            const hourValue = document.getElementById('hourValue');
            const minuteValue = document.getElementById('minuteValue');
            const selectedTime = document.getElementById('selectedTime');

            const updateTime = () => {
                const days = parseInt(daySlider.value);
                const hours = parseInt(hourSlider.value);
                const minutes = parseInt(minuteSlider.value);

                dayValue.textContent = days === 0 ? '今天' : `${days}天后`;
                hourValue.textContent = hours.toString().padStart(2, '0');
                minuteValue.textContent = minutes.toString().padStart(2, '0');

                let timeStr;
                if (days === 0) {
                    timeStr = `今天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                } else if (days === 1) {
                    timeStr = `明天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                } else {
                    timeStr = `${days}天后 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                }
                selectedTime.textContent = timeStr;
            };

            daySlider.addEventListener('input', updateTime);
            hourSlider.addEventListener('input', updateTime);
            minuteSlider.addEventListener('input', updateTime);

            updateTime();
        }

        function focusInput() {
            document.getElementById('taskInput').focus();
        }

        function addReminderDemo() {
            const input = document.getElementById('taskInput');
            if (input.value.trim()) {
                alert('演示模式：提醒已添加！\n在桌面应用中，这将创建真实的提醒。');
                input.value = '';
            } else {
                alert('请输入提醒内容');
            }
        }

        // 初始化演示
        setupDemoSliders();
    </script>
</body>
</html>
