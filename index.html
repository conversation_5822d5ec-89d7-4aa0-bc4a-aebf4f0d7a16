<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happ<PERSON> Reminder - 桌面便签</title>
    <link rel="stylesheet" href="sticky-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 自定义标题栏 -->
    <div class="titlebar">
        <div class="titlebar-drag">
            <i class="fas fa-sticky-note"></i>
            <span>便签提醒</span>
        </div>
        <div class="titlebar-controls">
            <button class="control-btn minimize-btn" onclick="minimizeWindow()">
                <i class="fas fa-minus"></i>
            </button>
            <button class="control-btn close-btn" onclick="closeWindow()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="sticky-container">
        <!-- 快速添加区域 -->
        <div class="quick-add">
            <div class="input-group">
                <input type="text" id="quickInput" placeholder="快速添加提醒..." maxlength="50">
                <button class="add-btn" onclick="quickAddReminder()">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        </div>

        <!-- 提醒列表 -->
        <div class="reminders-list" id="remindersList">
            <!-- 动态生成的提醒项 -->
        </div>

        <!-- 详细添加按钮 -->
        <div class="actions">
            <button class="detail-btn" onclick="toggleDetailForm()">
                <i class="fas fa-clock"></i> 定时提醒
            </button>
        </div>

        <!-- 详细添加表单（隐藏） -->
        <div class="detail-form" id="detailForm" style="display: none;">
            <div class="form-group">
                <input type="text" id="detailInput" placeholder="详细描述..." maxlength="100">
            </div>
            
            <div class="time-picker">
                <div class="time-row">
                    <label>时间:</label>
                    <select id="daySelect">
                        <option value="0">今天</option>
                        <option value="1">明天</option>
                        <option value="2">后天</option>
                        <option value="3">3天后</option>
                        <option value="7">1周后</option>
                    </select>
                    <input type="time" id="timeInput" value="12:00">
                </div>
            </div>
            
            <div class="form-actions">
                <button class="cancel-btn" onclick="toggleDetailForm()">取消</button>
                <button class="confirm-btn" onclick="addDetailReminder()">添加</button>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu" style="display: none;">
        <div class="menu-item" onclick="editReminder()">
            <i class="fas fa-edit"></i> 编辑
        </div>
        <div class="menu-item" onclick="deleteReminder()">
            <i class="fas fa-trash"></i> 删除
        </div>
        <div class="menu-item" onclick="markComplete()">
            <i class="fas fa-check"></i> 完成
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
