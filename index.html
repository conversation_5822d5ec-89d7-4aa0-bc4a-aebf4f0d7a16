<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happi Reminder - 桌面应用</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 自定义标题栏 -->
    <div class="titlebar">
        <div class="titlebar-drag">
            <i class="fas fa-bell"></i>
            <span>Happi Reminder</span>
        </div>
        <div class="titlebar-controls">
            <button class="control-btn minimize-btn" onclick="minimizeWindow()">
                <i class="fas fa-minus"></i>
            </button>
            <button class="control-btn close-btn" onclick="closeWindow()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="container">
        <!-- 主页面 -->
        <div class="main-page" id="mainPage">
            <!-- 快速添加区域 -->
            <div class="quick-add-section">
                <h2>快速便签</h2>
                <div class="quick-input-group">
                    <input type="text" id="quickTaskInput" placeholder="输入待办事项..." maxlength="100">
                    <button class="quick-add-btn" onclick="quickAddTask()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="quick-actions">
                    <button class="action-btn" onclick="toggleTimeForm()">
                        <i class="fas fa-clock"></i> 定时提醒
                    </button>
                    <button class="action-btn" onclick="showTaskList()">
                        <i class="fas fa-list"></i> 查看列表 (<span id="taskCount">0</span>)
                    </button>
                </div>
            </div>

            <!-- 定时提醒表单（隐藏） -->
            <div class="time-form" id="timeForm" style="display: none;">
                <h3>设置提醒时间</h3>
                <div class="form-group">
                    <input type="text" id="timedTaskInput" placeholder="输入待办事项..." maxlength="100">
                </div>

                <div class="time-slider-container">
                    <div class="time-sliders">
                        <div class="slider-group">
                            <label>天数</label>
                            <input type="range" id="daySlider" min="0" max="10" value="0" class="slider">
                            <span id="dayValue">今天</span>
                        </div>
                        <div class="slider-group">
                            <label>小时</label>
                            <input type="range" id="hourSlider" min="0" max="23" value="12" class="slider">
                            <span id="hourValue">12</span>
                        </div>
                        <div class="slider-group">
                            <label>分钟</label>
                            <input type="range" id="minuteSlider" min="0" max="59" value="0" step="5" class="slider">
                            <span id="minuteValue">00</span>
                        </div>
                    </div>
                    <div class="selected-time">
                        <i class="fas fa-clock"></i>
                        <span id="selectedTime">今天 12:00</span>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="cancel-btn" onclick="toggleTimeForm()">取消</button>
                    <button class="confirm-btn" onclick="addTimedReminder()">添加提醒</button>
                </div>
            </div>

            <!-- 固定按钮 -->
            <div class="pin-controls">
                <button class="pin-btn" id="pinBtn" onclick="togglePin()">
                    <i class="fas fa-thumbtack"></i> 固定位置
                </button>
            </div>
        </div>

        <!-- 任务列表页面 -->
        <div class="task-list-page" id="taskListPage" style="display: none;">
            <div class="page-header">
                <button class="back-btn" onclick="showMainPage()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>我的任务</h2>
                <button class="clear-btn" onclick="clearCompleted()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="task-list" id="taskList">
                <!-- 动态生成的任务项 -->
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
